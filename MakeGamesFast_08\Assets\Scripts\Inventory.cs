using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(VariableGridLayout), typeof(RectTransform))]
public class Inventory : MonoBehaviour
{
    public Vector2 InventorySize = new Vector2(4, 4);

    public InventorySlot[,] Slots;

    void Awake()
    {
        InitializeInventorySlots();
    }

    public void InitializeInventorySlots()
    {
        // TODO :Add logic that removes all items from the inventory before initializing it again

        // Initialize the 2D array based on InventorySize
        Slots = new InventorySlot[(int)InventorySize.x, (int)InventorySize.y];

        // Populate the array with empty slots
        for (int x = 0; x < InventorySize.x; x++)
        {
            for (int y = 0; y < InventorySize.y; y++)
            {
                Slots[x, y] = new InventorySlot.Builder(x, y).WithParent(transform).Build();
            }
        }
    }


}

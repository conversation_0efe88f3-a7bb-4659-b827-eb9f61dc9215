fileFormatVersion: 2
guid: 5f3147f7af4c49739579b966c458f5e4
timeCreated: 1488828285
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  isOverridable: 0
  platformData:
    Any:
      enabled: 0
      settings:
        Exclude Android: 1
        Exclude Editor: 0
        Exclude Linux: 1
        Exclude Linux64: 1
        Exclude LinuxUniversal: 1
        Exclude N3DS: 1
        Exclude OSXIntel: 1
        Exclude OSXIntel64: 1
        Exclude OSXUniversal: 1
        Exclude PS4: 1
        Exclude PSM: 1
        Exclude PSP2: 1
        Exclude SamsungTV: 1
        Exclude Tizen: 1
        Exclude WebGL: 1
        Exclude WiiU: 1
        Exclude Win: 1
        Exclude Win64: 1
        Exclude WindowsStoreApps: 1
        Exclude XboxOne: 1
        Exclude iOS: 1
        Exclude tvOS: 1
    Editor:
      enabled: 1
      settings:
        DefaultValueInitialized: true
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 89041
  packageName: Odin Inspector and Serializer
  packageVersion: 3.3.1.13
  assetPath: Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll
  uploadId: 758278

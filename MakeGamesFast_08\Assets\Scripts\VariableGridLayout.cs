using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("Layout/Variable Grid Layout")]
public class VariableGridLayout : LayoutGroup, ILayoutSelfController
{
    [Tooltip("Spacing between elements on the X (horizontal) axis.")]
    public float spacingX = 5f;
    [Tooltip("Spacing between elements on the Y (vertical) axis.")]
    public float spacingY = 5f;

    private int lastChildCount = 0;

    protected override void OnEnable()
    {
        base.OnEnable();
        lastChildCount = transform.childCount;
        SetDirty();
    }

    // protected override void OnValidate()
    // {
    //     base.OnValidate();
    //     SetDirty();
    // }

    void LateUpdate()
    {
        // Check if child count has changed (catches programmatic additions/removals)
        if (transform.childCount != lastChildCount)
        {
            lastChildCount = transform.childCount;
            SetDirty();
        }
    }

    public override void CalculateLayoutInputHorizontal()
    {
        base.CalculateLayoutInputHorizontal();
        float preferred = CalculatePreferredWidth();
        SetLayoutInputForAxis(preferred, preferred, -1f, 0);
    }

    public override void CalculateLayoutInputVertical()
    {
        float preferred = CalculatePreferredHeight();
        SetLayoutInputForAxis(preferred, preferred, -1f, 1);
    }

    public override void SetLayoutHorizontal() => LayoutChildren();
    public override void SetLayoutVertical() => LayoutChildren();

    private float CalculatePreferredWidth()
    {
        if (rectChildren.Count == 0)
            return padding.horizontal;

        // Calculate the maximum row width needed for horizontal layout
        float maxRowWidth = 0f;
        float currentRowWidth = padding.left;
        float availableWidth = rectTransform.rect.width - padding.horizontal;

        // If we have a ContentSizeFitter or no available width, don't wrap
        bool shouldWrap = availableWidth > 0 && GetComponent<ContentSizeFitter>() == null;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float childWidth = LayoutUtility.GetPreferredSize(child, 0);

            // Check if we need to wrap to next row
            if (shouldWrap && i > 0 && currentRowWidth + spacingX + childWidth > availableWidth)
            {
                maxRowWidth = Mathf.Max(maxRowWidth, currentRowWidth + padding.right);
                currentRowWidth = padding.left + childWidth;
            }
            else
            {
                if (i > 0) currentRowWidth += spacingX;
                currentRowWidth += childWidth;
            }
        }

        maxRowWidth = Mathf.Max(maxRowWidth, currentRowWidth + padding.right);
        return maxRowWidth;
    }

    private float CalculatePreferredHeight()
    {
        if (rectChildren.Count == 0)
            return padding.vertical;

        // Calculate total height needed for horizontal layout with wrapping
        float totalHeight = padding.top;
        float currentRowWidth = 0f;
        float currentRowHeight = 0f;
        float availableWidth = rectTransform.rect.width - padding.horizontal;

        // If we have a ContentSizeFitter or no available width, don't wrap
        bool shouldWrap = availableWidth > 0 && GetComponent<ContentSizeFitter>() == null;
        bool isFirstInRow = true;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float childWidth = LayoutUtility.GetPreferredSize(child, 0);
            float childHeight = LayoutUtility.GetPreferredSize(child, 1);

            // Check if we need to wrap to next row
            if (shouldWrap && !isFirstInRow && currentRowWidth + spacingX + childWidth > availableWidth)
            {
                // Finish current row
                totalHeight += currentRowHeight + spacingY;
                currentRowWidth = childWidth;
                currentRowHeight = childHeight;
                isFirstInRow = true;
            }
            else
            {
                // Add to current row
                if (!isFirstInRow)
                {
                    currentRowWidth += spacingX;
                }
                currentRowWidth += childWidth;
                currentRowHeight = Mathf.Max(currentRowHeight, childHeight);
                isFirstInRow = false;
            }
        }

        // Add the last row height
        totalHeight += currentRowHeight + padding.bottom;
        return totalHeight;
    }

    private void LayoutChildren()
    {
        if (rectChildren.Count == 0)
            return;

        // Layout children horizontally with wrapping
        float currentX = padding.left;
        float currentY = padding.top;
        float currentRowHeight = 0f;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float childWidth = LayoutUtility.GetPreferredSize(child, 0);
            float childHeight = LayoutUtility.GetPreferredSize(child, 1);

            // Check if we need to wrap to next row
            if (i > 0 && currentX + spacingX + childWidth > rectTransform.rect.width - padding.right)
            {
                // Move to next row
                currentX = padding.left;
                currentY += currentRowHeight + spacingY;
                currentRowHeight = 0f;
            }
            else if (i > 0)
            {
                // Add spacing for same row
                currentX += spacingX;
            }

            // Position the child
            SetChildAlongAxis(child, 0, currentX, childWidth);
            SetChildAlongAxis(child, 1, currentY, childHeight);

            // Update position and row height
            currentX += childWidth;
            currentRowHeight = Mathf.Max(currentRowHeight, childHeight);
        }
    }

    protected override void OnTransformChildrenChanged()
    {
        base.OnTransformChildrenChanged();
        SetDirty();
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();
        SetDirty();
    }

    protected override void OnDidApplyAnimationProperties()
    {
        base.OnDidApplyAnimationProperties();
        SetDirty();
    }

    /// <summary>
    /// Call this method after adding or removing children programmatically to force a layout update
    /// </summary>
    public void ForceLayoutUpdate()
    {
        SetDirty();
        LayoutRebuilder.ForceRebuildLayoutImmediate(rectTransform);
    }

    /// <summary>
    /// Call this method when you're about to add multiple children to prevent multiple layout updates
    /// </summary>
    public void BeginLayoutUpdate()
    {
        enabled = false;
    }

    /// <summary>
    /// Call this method after adding multiple children to trigger a single layout update
    /// </summary>
    public void EndLayoutUpdate()
    {
        enabled = true;
        ForceLayoutUpdate();
    }

    // protected override void Reset()
    // {
    //     base.Reset();
    //     spacingX = 5f;
    //     spacingY = 5f;
    // }
}

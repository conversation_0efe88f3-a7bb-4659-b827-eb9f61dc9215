using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class Item : MonoBehaviour
{
    public string Name;
    public Vector2 Size;

    public Item(string Name, Vector2 Size)
    {
        this.Name = Name;
        this.Size = Size;
    }

    public class Builder
    {
        public string Name;
        public Vector2 Size;

        public Builder(string name, Vector2 size) //Sprite icon, 
        {
            Name = name;
            //Icon = icon;
            Size = size;
        }

        public Item Build()
        {
            GameObject itemObject = new GameObject("Item_" + Name, typeof(RectTransform));
            Item item = itemObject.AddComponent<Item>();
            item.Name = Name;
            //item.Icon = Icon;
            item.Size = Size;

            Image image = itemObject.GetComponent<Image>();
            RectTransform rectTransform = itemObject.transform as RectTransform;
            rectTransform.sizeDelta = new Vector2(Size.x * InventoryStats.CELL_SIZE, Size.y * InventoryStats.CELL_SIZE);

            return item;
        }
    }
}
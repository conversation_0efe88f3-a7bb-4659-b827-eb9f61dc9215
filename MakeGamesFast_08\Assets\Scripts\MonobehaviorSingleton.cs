using Unity.VisualScripting;
using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using Sirenix.OdinInspector;
public class MonoBehaviourSingleton<T> : SerializedMonoBehaviour where T : MonoBehaviourSingleton<T>
{
    private static T instance;
    public static T Instance
    {
        get
        {
            if (instance == null)
            {
                T[] gameObjects = GameObject.FindObjectsByType<T>(FindObjectsSortMode.None);
                if (gameObjects == null || gameObjects.Length < 1)
                {
                    Debug.Log("Could not find any gameobjects object of type " + typeof(T).Name + " - created new gameobject");
                    return instance = new GameObject(typeof(T).Name).AddComponent<T>();
                }
                else if (gameObjects.Length > 1)
                {
                    Debug.LogWarning("Found more than one gameobject object of type " + typeof(T).Name);
                    for (int i = 1; i < gameObjects.Length; i++)
                    {
                        Debug.Log("Destroyed " + gameObjects[i].name);
                        Destroy(gameObjects[i]);
                    }
                }
                instance = gameObjects[0];
            }
            return instance;
        }
    }
}
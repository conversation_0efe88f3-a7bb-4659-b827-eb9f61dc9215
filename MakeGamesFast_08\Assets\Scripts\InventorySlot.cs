using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class InventorySlot : MonoBehaviour
{
    public int X;
    public int Y;
    public Item Item = null;
    private bool isInitialized = false;

    private void Awake()
    {
        Initialize();
    }

    public void Initialize()
    {
        if (isInitialized) return;

        // Perform initialization logic here
        RectTransform rectTransform = this.transform as RectTransform;
        rectTransform.sizeDelta = new Vector2(InventoryStats.CELL_SIZE, InventoryStats.CELL_SIZE);
        Debug.Log("Slot initialized");

        isInitialized = true;
    }

    public class Builder
    {
        public int X;
        public int Y;
        public Transform Parent = null;

        public Builder(int x, int y)
        {
            X = x;
            Y = y;
        }
        public Builder WithParent(Transform parent)
        {
            Parent = parent;
            return this;
        }


        public InventorySlot Build()
        {
            GameObject inventorySlotObject = new GameObject("InventorySlot_" + X + "_" + Y, typeof(RectTransform));
            InventorySlot inventorySlot = inventorySlotObject.AddComponent<InventorySlot>();
            inventorySlot.X = X;
            inventorySlot.Y = Y;
            inventorySlot.isInitialized = true;

            RectTransform rectTransform = inventorySlotObject.transform as RectTransform;
            rectTransform.sizeDelta = new Vector2(InventoryStats.CELL_SIZE, InventoryStats.CELL_SIZE);

            rectTransform.SetParent(Parent);

            return inventorySlot;
        }
    }
}
